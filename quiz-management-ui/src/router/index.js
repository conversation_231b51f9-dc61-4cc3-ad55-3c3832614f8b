import { createRouter, createWebHistory } from 'vue-router'
import AdminHome from '@/views/AdminHome.vue'

const routes = [
  { path: '/', name: 'Home', component: AdminHome },

  // Admin routes
  { path: '/create-assessment', name: 'CreateAssessment', component: () => import('@/views/CreateAssessment.vue') },
  { path: '/list-assessments', name: 'ListAssessments', component: () => import('@/views/ListAssessments.vue') },

  { path: '/generate-sessions', name: 'GenerateSessions', component: () => import('@/views/GenerateSessions.vue') },
  { path: '/generate-link', name: 'GenerateLink', component: () => import('@/views/GenerateLink.vue') },
  { path: '/take-quiz/:assessmentId', name: 'TakeQuiz', component: () => import('@/views/TakeQuiz.vue') },
  { path: '/create-skill', name: 'CreateSkill', component: () => import('@/views/CreateSkill.vue') },
  { path: '/list-skills', name: 'ListSkills', component: () => import('@/views/ListSkills.vue') },
  { path: '/skill/:id', name: 'SkillDetail', component: () => import('@/views/SkillDetail.vue') },
  { path: '/date-wise-report', name: 'DateWiseReport', component: () => import('@/views/DateWiseReport.vue') },
  { path: '/user-wise-report', name: 'UserWiseReport', component: () => import('@/views/UserWiseReport.vue') },
  { path: '/assessment-wise-report', name: 'AssessmentWiseReport', component: () => import('@/views/AssessmentWiseReport.vue') },

  { path: '/:pathMatch(.*)*', redirect: '/' },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

// Add global navigation guards for error handling
router.onError((error) => {
  console.error('Router error:', error);
});

// Add before each guard to handle potential navigation issues
router.beforeEach((to, from, next) => {
  try {
    // Check if the route exists
    if (!to.matched.length) {
      console.warn('Route not found:', to.path);
      next('/'); // Redirect to home if route not found
      return;
    }
    next();
  } catch (error) {
    console.error('Navigation error:', error);
    next('/'); // Redirect to home on error
  }
});

export default router
